import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from './contexts/ThemeContext';
import { UserProvider } from './contexts/UserContext';
import { LanguageProvider } from './contexts/LanguageContext';
import { OfflineProvider } from './contexts/OfflineContext';
import { AccessibilityProvider } from './contexts/AccessibilityContext';
import { SocialProvider } from './contexts/SocialContext';
import Sidebar from './components/layout/Sidebar';
import Header from './components/layout/Header';
import OfflineIndicator from './components/common/OfflineIndicator';
import Home from './pages/Home';
import Books from './pages/Books';
import Library from './pages/Library';
import Reader from './pages/Reader';
import BookClubs from './components/social/BookClubs';
import ReadingChallenges from './components/social/ReadingChallenges';
import './i18n';

function App() {
  return (
    <ThemeProvider>
      <AccessibilityProvider>
        <LanguageProvider>
          <OfflineProvider>
            <UserProvider>
              <SocialProvider>
                <Router>
                  <div className="min-h-screen bg-gray-50 dark:bg-gray-900 sepia:bg-amber-50 transition-colors duration-300">
                    <Routes>
                      {/* Reader route with full-screen layout */}
                      <Route path="/read/:bookId" element={<Reader />} />
                      
                      {/* Main app routes with sidebar layout */}
                      <Route
                        path="/*"
                        element={
                          <div className="flex">
                            <Sidebar />
                            <div className="flex-1 flex flex-col">
                              <Header />
                              <main className="flex-1 p-6">
                                <Routes>
                                  <Route path="/" element={<Home />} />
                                  <Route path="/books" element={<Books />} />
                                  <Route path="/library" element={<Library />} />
                                  <Route path="/book-clubs" element={<BookClubs />} />
                                  <Route path="/challenges" element={<ReadingChallenges />} />
                                  <Route path="/trending" element={<div>Trending</div>} />
                                  <Route path="/featured" element={<div>Featured</div>} />
                                  <Route path="/profile" element={<div>Profile</div>} />
                                  <Route path="/settings" element={<div>Settings</div>} />
                                </Routes>
                              </main>
                            </div>
                          </div>
                        }
                      />
                    </Routes>
                    
                    {/* Global Components */}
                    <OfflineIndicator />
                  </div>
                </Router>
              </SocialProvider>
            </UserProvider>
          </OfflineProvider>
        </LanguageProvider>
      </AccessibilityProvider>
    </ThemeProvider>
  );
}

export default App;