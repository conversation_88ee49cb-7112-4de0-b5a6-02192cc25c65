import React from 'react';
import { Trophy, Target, Calendar, Users, TrendingUp } from 'lucide-react';
import { useSocial } from '../../contexts/SocialContext';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';

const ReadingChallenges: React.FC = () => {
  const { challenges, joinChallenge } = useSocial();
  const { t } = useTranslation();

  const getProgressColor = (progress: number, target: number) => {
    const percentage = (progress / target) * 100;
    if (percentage >= 100) return 'bg-green-500';
    if (percentage >= 75) return 'bg-blue-500';
    if (percentage >= 50) return 'bg-yellow-500';
    return 'bg-gray-400';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          {t('social.readingChallenges')}
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Challenge yourself and compete with other readers
        </p>
      </div>

      {/* Active Challenges */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {challenges.map((challenge) => {
          const progressPercentage = (challenge.progress / challenge.target) * 100;
          const isCompleted = challenge.progress >= challenge.target;
          
          return (
            <motion.div
              key={challenge.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                    isCompleted 
                      ? 'bg-green-100 dark:bg-green-900/20' 
                      : 'bg-blue-100 dark:bg-blue-900/20'
                  }`}>
                    {isCompleted ? (
                      <Trophy size={24} className="text-green-600 dark:text-green-400" />
                    ) : (
                      <Target size={24} className="text-blue-600 dark:text-blue-400" />
                    )}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">
                      {challenge.title}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {challenge.description}
                    </p>
                  </div>
                </div>
                
                {isCompleted && (
                  <div className="bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 px-2 py-1 rounded-full text-xs font-medium">
                    Completed!
                  </div>
                )}
              </div>

              {/* Progress */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Progress
                  </span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {challenge.progress} / {challenge.target}
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full transition-all duration-500 ${getProgressColor(challenge.progress, challenge.target)}`}
                    style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                  />
                </div>
                <div className="text-center mt-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {Math.round(progressPercentage)}% Complete
                  </span>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    <Calendar size={16} className="text-gray-600 dark:text-gray-400" />
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400">Ends</p>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {new Date(challenge.endDate).toLocaleDateString()}
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    <Users size={16} className="text-gray-600 dark:text-gray-400" />
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400">Participants</p>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {challenge.participants.toLocaleString()}
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    <TrendingUp size={16} className="text-gray-600 dark:text-gray-400" />
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400">Rank</p>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    #42
                  </p>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => joinChallenge(challenge.id)}
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200"
                >
                  {t('social.joinChallenge')}
                </button>
                <button className="px-4 py-2 border border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                  {t('social.leaderboard')}
                </button>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Create Challenge CTA */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-bold mb-2">Create Your Own Challenge</h3>
            <p className="opacity-90">
              Set personal reading goals or challenge your friends
            </p>
          </div>
          <button className="bg-white/20 backdrop-blur-sm text-white px-6 py-3 rounded-lg hover:bg-white/30 transition-colors duration-200">
            Create Challenge
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReadingChallenges;