import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ChevronLeft, 
  ChevronRight, 
  Bookmark, 
  Settings, 
  List, 
  ArrowLeft,
  Sun,
  Moon,
  Palette,
  Type,
  AlignLeft,
  Minus,
  Plus,
  Volume2,
  Share2,
  MessageSquare
} from 'lucide-react';
import { useUser } from '../contexts/UserContext';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useSocial } from '../contexts/SocialContext';
import { useTranslation } from 'react-i18next';
import { mockBooks } from '../data/mockData';
import TextToSpeech from '../components/reader/TextToSpeech';

const Reader: React.FC = () => {
  const { bookId } = useParams<{ bookId: string }>();
  const navigate = useNavigate();
  const { user, updateReadingProgress } = useUser();
  const { theme, setTheme } = useTheme();
  const { isRTL } = useLanguage();
  const { shareHighlight } = useSocial();
  const { t } = useTranslation();
  
  const [currentPage, setCurrentPage] = useState(1);
  const [showSettings, setShowSettings] = useState(false);
  const [showTOC, setShowTOC] = useState(false);
  const [showTTS, setShowTTS] = useState(false);
  const [fontSize, setFontSize] = useState(16);
  const [fontFamily, setFontFamily] = useState('serif');
  const [lineHeight, setLineHeight] = useState(1.6);
  const [pageWidth, setPageWidth] = useState('max-w-4xl');
  const [selectedText, setSelectedText] = useState('');
  const [showTextActions, setShowTextActions] = useState(false);

  const book = mockBooks.find(b => b.id === bookId);
  const userBook = user?.library.find(ub => ub.bookId === bookId);

  useEffect(() => {
    if (userBook) {
      setCurrentPage(userBook.currentPage);
    }
  }, [userBook]);

  // Handle text selection
  useEffect(() => {
    const handleSelection = () => {
      const selection = window.getSelection();
      const text = selection?.toString().trim();
      if (text && text.length > 0) {
        setSelectedText(text);
        setShowTextActions(true);
      } else {
        setShowTextActions(false);
      }
    };

    document.addEventListener('mouseup', handleSelection);
    return () => document.removeEventListener('mouseup', handleSelection);
  }, []);

  if (!book) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Book not found
          </h2>
          <button 
            onClick={() => navigate('/library')}
            className="text-blue-600 dark:text-blue-400 hover:underline"
          >
            Return to Library
          </button>
        </div>
      </div>
    );
  }

  const totalPages = book.pages;
  const progress = Math.round((currentPage / totalPages) * 100);

  const handlePageChange = (page: number) => {
    const newPage = Math.max(1, Math.min(page, totalPages));
    setCurrentPage(newPage);
    const newProgress = Math.round((newPage / totalPages) * 100);
    updateReadingProgress(bookId!, newPage, newProgress);
  };

  const handleKeyPress = (e: KeyboardEvent) => {
    if (e.key === 'ArrowLeft') handlePageChange(currentPage - 1);
    if (e.key === 'ArrowRight') handlePageChange(currentPage + 1);
    if (e.key === 'Escape') {
      setShowSettings(false);
      setShowTOC(false);
      setShowTTS(false);
    }
  };

  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [currentPage]);

  const handleHighlight = () => {
    if (selectedText) {
      // Add highlight logic here
      console.log('Highlighting:', selectedText);
      setShowTextActions(false);
    }
  };

  const handleAddNote = () => {
    if (selectedText) {
      // Add note logic here
      console.log('Adding note for:', selectedText);
      setShowTextActions(false);
    }
  };

  const handleShareText = () => {
    if (selectedText) {
      shareHighlight(bookId!, selectedText);
      setShowTextActions(false);
    }
  };

  // Mock content for demonstration
  const sampleContent = `
    Chapter ${Math.ceil(currentPage / 20)}: ${book.title}
    
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
    
    Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
    
    Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.
    
    Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.
    
    Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem.
    
    Page ${currentPage} of ${totalPages}
  `;

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      theme === 'sepia' ? 'bg-amber-50 text-amber-900' :
      theme === 'dark' ? 'bg-gray-900 text-gray-100' :
      'bg-white text-gray-900'
    } ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <header className="sticky top-0 z-50 bg-white/80 dark:bg-gray-900/80 sepia:bg-amber-50/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-800 sepia:border-amber-200">
        <div className={`flex items-center justify-between px-6 py-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center space-x-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
            <button
              onClick={() => navigate('/library')}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 sepia:hover:bg-amber-100 rounded-lg transition-colors duration-200"
            >
              <ArrowLeft size={20} />
            </button>
            <div className={isRTL ? 'text-right' : ''}>
              <h1 className="font-semibold text-lg">{book.title}</h1>
              <p className="text-sm text-gray-600 dark:text-gray-400 sepia:text-amber-700">
                by {book.author}
              </p>
            </div>
          </div>

          <div className={`flex items-center space-x-2 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
            {/* Progress */}
            <div className={`hidden sm:flex items-center space-x-3 text-sm ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
              <span>{progress}%</span>
              <div className="w-32 h-2 bg-gray-200 dark:bg-gray-700 sepia:bg-amber-200 rounded-full">
                <div
                  className="h-full bg-blue-600 dark:bg-blue-400 sepia:bg-amber-600 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
              <span>{currentPage}/{totalPages}</span>
            </div>

            {/* Controls */}
            <button
              onClick={() => setShowTOC(!showTOC)}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 sepia:hover:bg-amber-100 rounded-lg transition-colors duration-200"
              title={t('reader.tableOfContents')}
            >
              <List size={20} />
            </button>
            
            <button
              onClick={() => setShowTTS(!showTTS)}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 sepia:hover:bg-amber-100 rounded-lg transition-colors duration-200"
              title={t('reader.textToSpeech')}
            >
              <Volume2 size={20} />
            </button>
            
            <button
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 sepia:hover:bg-amber-100 rounded-lg transition-colors duration-200"
              title={t('reader.addBookmark')}
            >
              <Bookmark size={20} />
            </button>
            
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 sepia:hover:bg-amber-100 rounded-lg transition-colors duration-200"
              title={t('reader.settings')}
            >
              <Settings size={20} />
            </button>
          </div>
        </div>
      </header>

      {/* Text Selection Actions */}
      {showTextActions && (
        <div className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-2 flex items-center space-x-2">
          <button
            onClick={handleHighlight}
            className="px-3 py-1 text-sm bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200 rounded hover:bg-yellow-200 dark:hover:bg-yellow-900/40 transition-colors duration-200"
          >
            {t('reader.highlight')}
          </button>
          <button
            onClick={handleAddNote}
            className="px-3 py-1 text-sm bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded hover:bg-blue-200 dark:hover:bg-blue-900/40 transition-colors duration-200"
          >
            {t('reader.addNote')}
          </button>
          <button
            onClick={handleShareText}
            className="px-3 py-1 text-sm bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 rounded hover:bg-green-200 dark:hover:bg-green-900/40 transition-colors duration-200"
          >
            {t('reader.share')}
          </button>
        </div>
      )}

      {/* Settings Panel */}
      {showSettings && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 sepia:bg-amber-50 rounded-2xl p-6 max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold">{t('reader.settings')}</h3>
              <button
                onClick={() => setShowSettings(false)}
                className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
              >
                ×
              </button>
            </div>

            <div className="space-y-6">
              {/* Theme */}
              <div>
                <label className="block text-sm font-medium mb-3">{t('reader.theme')}</label>
                <div className="grid grid-cols-3 gap-2">
                  {[
                    { key: 'light', icon: Sun, label: t('reader.light') },
                    { key: 'dark', icon: Moon, label: t('reader.dark') },
                    { key: 'sepia', icon: Palette, label: t('reader.sepia') }
                  ].map(({ key, icon: Icon, label }) => (
                    <button
                      key={key}
                      onClick={() => setTheme(key as any)}
                      className={`flex flex-col items-center p-3 rounded-lg border-2 transition-colors duration-200 ${
                        theme === key
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 sepia:bg-amber-100'
                          : 'border-gray-200 dark:border-gray-700 sepia:border-amber-300 hover:border-gray-300'
                      }`}
                    >
                      <Icon size={20} className="mb-1" />
                      <span className="text-xs">{label}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Font Size */}
              <div>
                <label className="block text-sm font-medium mb-3">{t('reader.fontSize')}</label>
                <div className="flex items-center justify-between">
                  <button
                    onClick={() => setFontSize(Math.max(12, fontSize - 2))}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 sepia:hover:bg-amber-100 rounded-lg"
                  >
                    <Minus size={16} />
                  </button>
                  <span className="text-lg">{fontSize}px</span>
                  <button
                    onClick={() => setFontSize(Math.min(24, fontSize + 2))}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 sepia:hover:bg-amber-100 rounded-lg"
                  >
                    <Plus size={16} />
                  </button>
                </div>
              </div>

              {/* Font Family */}
              <div>
                <label className="block text-sm font-medium mb-3">{t('reader.fontFamily')}</label>
                <select
                  value={fontFamily}
                  onChange={(e) => setFontFamily(e.target.value)}
                  className="w-full p-2 border border-gray-200 dark:border-gray-700 sepia:border-amber-300 rounded-lg bg-white dark:bg-gray-700 sepia:bg-amber-50"
                >
                  <option value="serif">Serif</option>
                  <option value="sans-serif">Sans Serif</option>
                  <option value="monospace">Monospace</option>
                </select>
              </div>

              {/* Line Height */}
              <div>
                <label className="block text-sm font-medium mb-3">{t('reader.lineHeight')}</label>
                <input
                  type="range"
                  min="1.2"
                  max="2.0"
                  step="0.1"
                  value={lineHeight}
                  onChange={(e) => setLineHeight(Number(e.target.value))}
                  className="w-full"
                />
                <div className="text-center text-sm text-gray-600 dark:text-gray-400 sepia:text-amber-700">
                  {lineHeight}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Text to Speech Panel */}
      {showTTS && (
        <div className="fixed bottom-20 right-6 z-50 w-80">
          <TextToSpeech text={sampleContent} isVisible={showTTS} />
        </div>
      )}

      {/* Main Content */}
      <main className="flex-1">
        <div className={`mx-auto px-6 py-8 ${pageWidth}`}>
          <div 
            className="prose prose-lg max-w-none"
            style={{
              fontSize: `${fontSize}px`,
              fontFamily,
              lineHeight,
              direction: isRTL ? 'rtl' : 'ltr'
            }}
          >
            <div className="whitespace-pre-line">
              {sampleContent}
            </div>
          </div>
        </div>
      </main>

      {/* Navigation */}
      <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2">
        <div className={`flex items-center space-x-4 bg-white/90 dark:bg-gray-800/90 sepia:bg-amber-50/90 backdrop-blur-sm rounded-full px-6 py-3 shadow-lg border border-gray-200 dark:border-gray-700 sepia:border-amber-200 ${
          isRTL ? 'flex-row-reverse space-x-reverse' : ''
        }`}>
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 sepia:hover:bg-amber-100 rounded-full transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronLeft size={20} />
          </button>
          
          <div className={`flex items-center space-x-3 text-sm ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
            <input
              type="number"
              value={currentPage}
              onChange={(e) => handlePageChange(Number(e.target.value))}
              className="w-16 text-center bg-transparent border-none outline-none"
              min="1"
              max={totalPages}
            />
            <span>/</span>
            <span>{totalPages}</span>
          </div>
          
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 sepia:hover:bg-amber-100 rounded-full transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronRight size={20} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default Reader;