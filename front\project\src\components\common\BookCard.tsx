import React, { useState } from 'react';
import { Star, ShoppingCart, Eye, Heart, Download, DownloadCloud } from 'lucide-react';
import { Book } from '../../types';
import { useUser } from '../../contexts/UserContext';
import { useOffline } from '../../contexts/OfflineContext';
import { useTranslation } from 'react-i18next';
import BookPreview from './BookPreview';

interface BookCardProps {
  book: Book;
  onClick?: () => void;
  showPrice?: boolean;
  size?: 'small' | 'medium' | 'large';
}

const BookCard: React.FC<BookCardProps> = ({ 
  book, 
  onClick, 
  showPrice = true,
  size = 'medium' 
}) => {
  const { addToLibrary } = useUser();
  const { downloadBook, isBookDownloaded, isOnline } = useOffline();
  const { t } = useTranslation();
  const [showPreview, setShowPreview] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    addToLibrary(book);
  };

  const handlePreview = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowPreview(true);
  };

  const handleDownload = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isBookDownloaded(book.id)) return;
    
    setIsDownloading(true);
    try {
      await downloadBook(book.id, book);
    } catch (error) {
      console.error('Download failed:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  const handleWishlist = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Add to wishlist
  };

  const cardSizes = {
    small: 'w-40',
    medium: 'w-48',
    large: 'w-56'
  };

  const imageSizes = {
    small: 'h-52',
    medium: 'h-64',
    large: 'h-72'
  };

  const isDownloaded = isBookDownloaded(book.id);

  return (
    <>
      <div 
        className={`${cardSizes[size]} group cursor-pointer`}
        onClick={onClick}
      >
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-105">
          {/* Book Cover */}
          <div className="relative overflow-hidden">
            <img
              src={book.cover}
              alt={book.title}
              className={`w-full ${imageSizes[size]} object-cover transition-transform duration-300 group-hover:scale-110`}
            />
            
            {/* Overlay Actions */}
            <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-3">
              <button
                onClick={handlePreview}
                className="p-2 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors duration-200"
                title={t('common.preview')}
              >
                <Eye size={18} />
              </button>
              
              <button
                onClick={handleDownload}
                disabled={isDownloading || !isOnline}
                className={`p-2 backdrop-blur-sm rounded-full text-white transition-colors duration-200 ${
                  isDownloaded 
                    ? 'bg-green-600/80 hover:bg-green-700/80' 
                    : 'bg-white/20 hover:bg-white/30'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
                title={isDownloaded ? 'Downloaded' : t('common.download')}
              >
                {isDownloading ? (
                  <DownloadCloud size={18} className="animate-pulse" />
                ) : isDownloaded ? (
                  <Download size={18} />
                ) : (
                  <DownloadCloud size={18} />
                )}
              </button>
              
              <button
                onClick={handleWishlist}
                className="p-2 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors duration-200"
                title="Add to Wishlist"
              >
                <Heart size={18} />
              </button>
              
              {showPrice && (
                <button
                  onClick={handleAddToCart}
                  className="p-2 bg-blue-600 rounded-full text-white hover:bg-blue-700 transition-colors duration-200"
                  title={t('common.addToLibrary')}
                >
                  <ShoppingCart size={18} />
                </button>
              )}
            </div>

            {/* Category Badge */}
            <div className="absolute top-3 left-3">
              <span className="px-2 py-1 bg-black/60 backdrop-blur-sm text-white text-xs rounded-full">
                {book.category}
              </span>
            </div>

            {/* Download Status */}
            {isDownloaded && (
              <div className="absolute top-3 right-3">
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <Download size={12} className="text-white" />
                </div>
              </div>
            )}
          </div>

          {/* Book Info */}
          <div className="p-4">
            <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-1 line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">
              {book.title}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-xs mb-2">
              by {book.author}
            </p>

            {/* Rating */}
            <div className="flex items-center space-x-1 mb-3">
              <Star className="text-yellow-400 fill-current" size={12} />
              <span className="text-xs font-medium text-gray-900 dark:text-white">
                {book.rating}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                ({book.reviews})
              </span>
            </div>

            {/* Price */}
            {showPrice && (
              <div className="flex items-center justify-between">
                <span className="text-lg font-bold text-blue-600 dark:text-blue-400">
                  ${book.price}
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {book.pages} pages
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Book Preview Modal */}
      <BookPreview 
        book={book}
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
      />
    </>
  );
};

export default BookCard;