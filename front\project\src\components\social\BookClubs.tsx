import React, { useState } from 'react';
import { Users, Plus, MessageCircle, BookOpen, Lock, Globe } from 'lucide-react';
import { useSocial } from '../../contexts/SocialContext';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';

const BookClubs: React.FC = () => {
  const { bookClubs, joinBookClub, createBookClub } = useSocial();
  const { t } = useTranslation();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newClub, setNewClub] = useState({
    name: '',
    description: '',
    currentBook: '',
    isPrivate: false,
    createdBy: 'current-user'
  });

  const handleCreateClub = (e: React.FormEvent) => {
    e.preventDefault();
    if (newClub.name.trim()) {
      createBookClub({
        ...newClub,
        members: 1
      });
      setNewClub({
        name: '',
        description: '',
        currentBook: '',
        isPrivate: false,
        createdBy: 'current-user'
      });
      setShowCreateForm(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('social.bookClubs')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Join communities of readers and discuss your favorite books
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200"
        >
          <Plus size={20} />
          <span>{t('social.createClub')}</span>
        </button>
      </div>

      {/* Create Club Form */}
      {showCreateForm && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('social.createClub')}
          </h3>
          <form onSubmit={handleCreateClub} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Club Name
              </label>
              <input
                type="text"
                value={newClub.name}
                onChange={(e) => setNewClub(prev => ({ ...prev, name: e.target.value }))}
                className="w-full p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="Enter club name..."
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description
              </label>
              <textarea
                value={newClub.description}
                onChange={(e) => setNewClub(prev => ({ ...prev, description: e.target.value }))}
                className="w-full p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                rows={3}
                placeholder="Describe your book club..."
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Current Book
              </label>
              <input
                type="text"
                value={newClub.currentBook}
                onChange={(e) => setNewClub(prev => ({ ...prev, currentBook: e.target.value }))}
                className="w-full p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="What book are you currently reading?"
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="isPrivate"
                checked={newClub.isPrivate}
                onChange={(e) => setNewClub(prev => ({ ...prev, isPrivate: e.target.checked }))}
                className="rounded border-gray-300 dark:border-gray-600"
              />
              <label htmlFor="isPrivate" className="text-sm text-gray-700 dark:text-gray-300">
                Private club (invite only)
              </label>
            </div>
            <div className="flex items-center space-x-3">
              <button
                type="submit"
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200"
              >
                Create Club
              </button>
              <button
                type="button"
                onClick={() => setShowCreateForm(false)}
                className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
              >
                Cancel
              </button>
            </div>
          </form>
        </motion.div>
      )}

      {/* Book Clubs Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {bookClubs.map((club) => (
          <motion.div
            key={club.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200"
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <Users size={20} className="text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">
                    {club.name}
                  </h3>
                  <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                    {club.isPrivate ? (
                      <Lock size={14} />
                    ) : (
                      <Globe size={14} />
                    )}
                    <span>{club.members} {t('social.members')}</span>
                  </div>
                </div>
              </div>
            </div>

            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
              {club.description}
            </p>

            {club.currentBook && (
              <div className="flex items-center space-x-2 mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <BookOpen size={16} className="text-blue-600 dark:text-blue-400" />
                <div>
                  <p className="text-xs text-gray-600 dark:text-gray-400">Currently Reading</p>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {club.currentBook}
                  </p>
                </div>
              </div>
            )}

            <div className="flex items-center justify-between">
              <button
                onClick={() => joinBookClub(club.id)}
                className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200"
              >
                <Users size={16} />
                <span>{t('social.joinClub')}</span>
              </button>
              
              <button className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors duration-200">
                <MessageCircle size={16} />
                <span className="text-sm">{t('social.discussions')}</span>
              </button>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default BookClubs;