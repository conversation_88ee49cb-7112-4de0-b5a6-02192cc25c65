import React from 'react';
import { Wifi, WifiOff, RefreshCw } from 'lucide-react';
import { useOffline } from '../../contexts/OfflineContext';
import { useTranslation } from 'react-i18next';

const OfflineIndicator: React.FC = () => {
  const { isOnline, isSyncing, syncData } = useOffline();
  const { t } = useTranslation();

  if (isOnline && !isSyncing) return null;

  return (
    <div className={`fixed top-4 right-4 z-50 flex items-center space-x-2 px-4 py-2 rounded-lg shadow-lg transition-all duration-300 ${
      isOnline 
        ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200'
        : 'bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-200'
    }`}>
      {isSyncing ? (
        <RefreshCw size={16} className="animate-spin" />
      ) : isOnline ? (
        <Wifi size={16} />
      ) : (
        <WifiOff size={16} />
      )}
      <span className="text-sm font-medium">
        {isSyncing ? t('common.syncing') : isOnline ? t('common.online') : t('common.offline')}
      </span>
      {!isOnline && (
        <button
          onClick={syncData}
          className="text-xs underline hover:no-underline"
        >
          {t('common.retry')}
        </button>
      )}
    </div>
  );
};

export default OfflineIndicator;