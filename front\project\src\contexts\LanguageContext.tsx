import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

type Language = 'en' | 'am';
type Direction = 'ltr' | 'rtl';

interface LanguageContextType {
  language: Language;
  direction: Direction;
  setLanguage: (lang: Language) => void;
  isRTL: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { i18n } = useTranslation();
  const [language, setLanguageState] = useState<Language>('en');
  const [direction, setDirection] = useState<Direction>('ltr');

  const setLanguage = useCallback((lang: Language) => {
    setLanguageState(lang);
    i18n.changeLanguage(lang);
    
    // Set direction based on language
    const newDirection = lang === 'am' ? 'rtl' : 'ltr';
    setDirection(newDirection);
    
    // Update document attributes
    document.documentElement.lang = lang;
    document.documentElement.dir = newDirection;
    
    // Store preference
    localStorage.setItem('preferred-language', lang);
  }, [i18n]);

  useEffect(() => {
    // Load saved language preference
    const savedLang = localStorage.getItem('preferred-language') as Language;
    if (savedLang && (savedLang === 'en' || savedLang === 'am')) {
      setLanguage(savedLang);
    }
  }, [setLanguage]);

  const isRTL = direction === 'rtl';

  return (
    <LanguageContext.Provider value={{ language, direction, setLanguage, isRTL }}>
      {children}
    </LanguageContext.Provider>
  );
};