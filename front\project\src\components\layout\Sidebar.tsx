import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { 
  Home, 
  BookOpen, 
  Package, 
  Library, 
  User, 
  Settings, 
  Users,
  Trophy,
  TrendingUp,
  Clock
} from 'lucide-react';
import { useUser } from '../../contexts/UserContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { useTranslation } from 'react-i18next';

const Sidebar: React.FC = () => {
  const location = useLocation();
  const { user } = useUser();
  const { isRTL } = useLanguage();
  const { t } = useTranslation();

  const mainNavItems = [
    { to: '/', icon: Home, label: t('nav.home') },
    { to: '/books', icon: BookOpen, label: t('nav.books') },
    { to: '/library', icon: Library, label: t('nav.library') },
  ];

  const socialItems = [
    { to: '/book-clubs', icon: Users, label: t('nav.bookClubs') },
    { to: '/challenges', icon: Trophy, label: t('nav.challenges') },
  ];

  const discoverItems = [
    { to: '/trending', icon: TrendingUp, label: 'Trending' },
    { to: '/featured', icon: Clock, label: 'Featured' },
  ];

  const userItems = [
    { to: '/profile', icon: User, label: t('nav.profile') },
    { to: '/settings', icon: Settings, label: t('nav.settings') },
  ];

  const NavItem: React.FC<{
    to: string;
    icon: React.ElementType;
    label: string;
    count?: number;
  }> = ({ to, icon: Icon, label, count }) => {
    const isActive = location.pathname === to;

    return (
      <NavLink
        to={to}
        className={`flex items-center justify-between px-3 py-2.5 rounded-xl text-sm font-medium transition-all duration-200 group hover:bg-gray-100 dark:hover:bg-gray-800 ${
          isActive
            ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400'
            : 'text-gray-700 dark:text-gray-300'
        } ${isRTL ? 'flex-row-reverse' : ''}`}
      >
        <div className={`flex items-center space-x-3 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
          <Icon 
            size={18} 
            className={`transition-colors duration-200 ${
              isActive ? 'text-blue-600 dark:text-blue-400' : ''
            }`} 
          />
          <span>{label}</span>
        </div>
        {count && (
          <span className="bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs px-2 py-0.5 rounded-full">
            {count}
          </span>
        )}
      </NavLink>
    );
  };

  return (
    <aside className={`w-64 bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-800 flex flex-col ${
      isRTL ? 'border-l' : 'border-r'
    }`}>
      {/* Logo */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-800">
        <div className={`flex items-center space-x-3 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
          <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
            <BookOpen size={20} className="text-white" />
          </div>
          <h1 className="text-xl font-bold text-gray-900 dark:text-white">Astewai</h1>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 px-4 py-6 space-y-8">
        {/* Main Navigation */}
        <nav className="space-y-1">
          {mainNavItems.map((item) => (
            <NavItem
              key={item.to}
              to={item.to}
              icon={item.icon}
              label={item.label}
              count={item.label === t('nav.library') ? user?.library.length : undefined}
            />
          ))}
        </nav>

        {/* Social Section */}
        <div>
          <h3 className={`px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3 ${
            isRTL ? 'text-right' : ''
          }`}>
            Social
          </h3>
          <nav className="space-y-1">
            {socialItems.map((item) => (
              <NavItem
                key={item.to}
                to={item.to}
                icon={item.icon}
                label={item.label}
              />
            ))}
          </nav>
        </div>

        {/* Discover Section */}
        <div>
          <h3 className={`px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3 ${
            isRTL ? 'text-right' : ''
          }`}>
            Discover
          </h3>
          <nav className="space-y-1">
            {discoverItems.map((item) => (
              <NavItem
                key={item.to}
                to={item.to}
                icon={item.icon}
                label={item.label}
              />
            ))}
          </nav>
        </div>

        {/* User Section */}
        {user && (
          <div>
            <h3 className={`px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3 ${
              isRTL ? 'text-right' : ''
            }`}>
              Account
            </h3>
            <nav className="space-y-1">
              {userItems.map((item) => (
                <NavItem
                  key={item.to}
                  to={item.to}
                  icon={item.icon}
                  label={item.label}
                />
              ))}
            </nav>
          </div>
        )}
      </div>

      {/* User Profile */}
      {user && (
        <div className="p-4 border-t border-gray-200 dark:border-gray-800">
          <div className={`flex items-center space-x-3 p-3 rounded-xl bg-gray-50 dark:bg-gray-800 ${
            isRTL ? 'flex-row-reverse space-x-reverse' : ''
          }`}>
            <img
              src={user.avatar}
              alt={user.name}
              className="w-8 h-8 rounded-full object-cover"
            />
            <div className={`flex-1 min-w-0 ${isRTL ? 'text-right' : ''}`}>
              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                {user.name}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                {user.email}
              </p>
            </div>
          </div>
        </div>
      )}
    </aside>
  );
};

export default Sidebar;