import React, { createContext, useContext, useState, useCallback } from 'react';

interface BookClub {
  id: string;
  name: string;
  description: string;
  members: number;
  currentBook: string;
  isPrivate: boolean;
  createdBy: string;
  createdAt: string;
}

interface ReadingChallenge {
  id: string;
  title: string;
  description: string;
  target: number;
  progress: number;
  startDate: string;
  endDate: string;
  participants: number;
}

interface Discussion {
  id: string;
  bookId: string;
  title: string;
  content: string;
  author: string;
  replies: number;
  createdAt: string;
}

interface SocialContextType {
  bookClubs: BookClub[];
  challenges: ReadingChallenge[];
  discussions: Discussion[];
  joinBookClub: (clubId: string) => void;
  leaveBookClub: (clubId: string) => void;
  createBookClub: (club: Omit<BookClub, 'id' | 'createdAt'>) => void;
  joinChallenge: (challengeId: string) => void;
  createDiscussion: (discussion: Omit<Discussion, 'id' | 'createdAt' | 'replies'>) => void;
  shareHighlight: (bookId: string, text: string, note?: string) => void;
}

const SocialContext = createContext<SocialContextType | undefined>(undefined);

export const useSocial = () => {
  const context = useContext(SocialContext);
  if (!context) {
    throw new Error('useSocial must be used within a SocialProvider');
  }
  return context;
};

export const SocialProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [bookClubs, setBookClubs] = useState<BookClub[]>([
    {
      id: '1',
      name: 'Science Fiction Enthusiasts',
      description: 'Exploring the future through literature',
      members: 156,
      currentBook: 'Project Hail Mary',
      isPrivate: false,
      createdBy: 'user1',
      createdAt: '2024-01-01'
    },
    {
      id: '2',
      name: 'Ethiopian Literature Circle',
      description: 'የኢትዮጵያ ሥነ-ጽሑፍ ክበብ',
      members: 89,
      currentBook: 'Fikir Eske Mekabir',
      isPrivate: false,
      createdBy: 'user2',
      createdAt: '2024-01-15'
    }
  ]);

  const [challenges, setChallenges] = useState<ReadingChallenge[]>([
    {
      id: '1',
      title: '2024 Reading Challenge',
      description: 'Read 52 books this year',
      target: 52,
      progress: 12,
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      participants: 1247
    },
    {
      id: '2',
      title: 'Diverse Voices Challenge',
      description: 'Read books by authors from different cultures',
      target: 12,
      progress: 3,
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      participants: 892
    }
  ]);

  const [discussions, setDiscussions] = useState<Discussion[]>([
    {
      id: '1',
      bookId: '6',
      title: 'What did you think of the ending?',
      content: 'I found the resolution quite satisfying...',
      author: 'Sarah Johnson',
      replies: 23,
      createdAt: '2024-01-20'
    }
  ]);

  const joinBookClub = useCallback((clubId: string) => {
    setBookClubs(prev => prev.map(club => 
      club.id === clubId ? { ...club, members: club.members + 1 } : club
    ));
  }, []);

  const leaveBookClub = useCallback((clubId: string) => {
    setBookClubs(prev => prev.map(club => 
      club.id === clubId ? { ...club, members: Math.max(0, club.members - 1) } : club
    ));
  }, []);

  const createBookClub = useCallback((club: Omit<BookClub, 'id' | 'createdAt'>) => {
    const newClub: BookClub = {
      ...club,
      id: Date.now().toString(),
      createdAt: new Date().toISOString()
    };
    setBookClubs(prev => [...prev, newClub]);
  }, []);

  const joinChallenge = useCallback((challengeId: string) => {
    setChallenges(prev => prev.map(challenge => 
      challenge.id === challengeId 
        ? { ...challenge, participants: challenge.participants + 1 }
        : challenge
    ));
  }, []);

  const createDiscussion = useCallback((discussion: Omit<Discussion, 'id' | 'createdAt' | 'replies'>) => {
    const newDiscussion: Discussion = {
      ...discussion,
      id: Date.now().toString(),
      replies: 0,
      createdAt: new Date().toISOString()
    };
    setDiscussions(prev => [...prev, newDiscussion]);
  }, []);

  const shareHighlight = useCallback((bookId: string, text: string, note?: string) => {
    // In a real app, this would share to social media or within the platform
    console.log('Sharing highlight:', { bookId, text, note });
  }, []);

  return (
    <SocialContext.Provider value={{
      bookClubs,
      challenges,
      discussions,
      joinBookClub,
      leaveBookClub,
      createBookClub,
      joinChallenge,
      createDiscussion,
      shareHighlight
    }}>
      {children}
    </SocialContext.Provider>
  );
};