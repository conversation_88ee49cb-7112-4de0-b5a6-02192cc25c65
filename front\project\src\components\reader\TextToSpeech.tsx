import React, { useState, useEffect, useCallback } from 'react';
import { Play, Pause, Square, SkipBack, SkipForward, Volume2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface TextToSpeechProps {
  text: string;
  isVisible: boolean;
}

const TextToSpeech: React.FC<TextToSpeechProps> = ({ text, isVisible }) => {
  const { t } = useTranslation();
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [rate, setRate] = useState(1);
  const [volume, setVolume] = useState(0.8);
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<SpeechSynthesisVoice | null>(null);
  const [utterance, setUtterance] = useState<SpeechSynthesisUtterance | null>(null);

  // Load available voices
  useEffect(() => {
    const loadVoices = () => {
      const availableVoices = speechSynthesis.getVoices();
      setVoices(availableVoices);
      
      // Try to find a good default voice
      const defaultVoice = availableVoices.find(voice => 
        voice.lang.startsWith('en') && voice.default
      ) || availableVoices[0];
      
      setSelectedVoice(defaultVoice);
    };

    loadVoices();
    speechSynthesis.addEventListener('voiceschanged', loadVoices);

    return () => {
      speechSynthesis.removeEventListener('voiceschanged', loadVoices);
    };
  }, []);

  // Create utterance when text or settings change
  useEffect(() => {
    if (text && selectedVoice) {
      const newUtterance = new SpeechSynthesisUtterance(text);
      newUtterance.voice = selectedVoice;
      newUtterance.rate = rate;
      newUtterance.volume = volume;
      
      newUtterance.onstart = () => {
        setIsPlaying(true);
        setIsPaused(false);
      };
      
      newUtterance.onend = () => {
        setIsPlaying(false);
        setIsPaused(false);
      };
      
      newUtterance.onpause = () => {
        setIsPaused(true);
      };
      
      newUtterance.onresume = () => {
        setIsPaused(false);
      };
      
      setUtterance(newUtterance);
    }
  }, [text, selectedVoice, rate, volume]);

  const handlePlay = useCallback(() => {
    if (utterance) {
      if (isPaused) {
        speechSynthesis.resume();
      } else {
        speechSynthesis.cancel(); // Stop any current speech
        speechSynthesis.speak(utterance);
      }
    }
  }, [utterance, isPaused]);

  const handlePause = useCallback(() => {
    speechSynthesis.pause();
  }, []);

  const handleStop = useCallback(() => {
    speechSynthesis.cancel();
    setIsPlaying(false);
    setIsPaused(false);
  }, []);

  const handleRateChange = useCallback((newRate: number) => {
    setRate(newRate);
    if (isPlaying) {
      handleStop();
    }
  }, [isPlaying, handleStop]);

  const handleVolumeChange = useCallback((newVolume: number) => {
    setVolume(newVolume);
  }, []);

  if (!isVisible || !('speechSynthesis' in window)) {
    return null;
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-gray-900 dark:text-white">
          {t('reader.textToSpeech')}
        </h3>
        <Volume2 size={20} className="text-gray-600 dark:text-gray-400" />
      </div>

      {/* Playback Controls */}
      <div className="flex items-center justify-center space-x-3">
        <button
          onClick={() => handleRateChange(Math.max(0.5, rate - 0.25))}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
          title="Slower"
        >
          <SkipBack size={20} />
        </button>
        
        {isPlaying && !isPaused ? (
          <button
            onClick={handlePause}
            className="p-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors duration-200"
            title={t('reader.pause')}
          >
            <Pause size={24} />
          </button>
        ) : (
          <button
            onClick={handlePlay}
            className="p-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors duration-200"
            title={t('reader.play')}
          >
            <Play size={24} />
          </button>
        )}
        
        <button
          onClick={handleStop}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
          title="Stop"
        >
          <Square size={20} />
        </button>
        
        <button
          onClick={() => handleRateChange(Math.min(2, rate + 0.25))}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
          title="Faster"
        >
          <SkipForward size={20} />
        </button>
      </div>

      {/* Speed Control */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {t('reader.speed')}
          </label>
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {rate}x
          </span>
        </div>
        <input
          type="range"
          min="0.5"
          max="2"
          step="0.25"
          value={rate}
          onChange={(e) => handleRateChange(Number(e.target.value))}
          className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
        />
      </div>

      {/* Volume Control */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Volume
          </label>
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {Math.round(volume * 100)}%
          </span>
        </div>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={volume}
          onChange={(e) => handleVolumeChange(Number(e.target.value))}
          className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
        />
      </div>

      {/* Voice Selection */}
      {voices.length > 0 && (
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Voice
          </label>
          <select
            value={selectedVoice?.name || ''}
            onChange={(e) => {
              const voice = voices.find(v => v.name === e.target.value);
              setSelectedVoice(voice || null);
            }}
            className="w-full p-2 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            {voices.map((voice) => (
              <option key={voice.name} value={voice.name}>
                {voice.name} ({voice.lang})
              </option>
            ))}
          </select>
        </div>
      )}
    </div>
  );
};

export default TextToSpeech;