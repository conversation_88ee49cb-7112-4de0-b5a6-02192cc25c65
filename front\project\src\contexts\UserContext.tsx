import React, { createContext, useContext, useState } from 'react';
import { User, UserBook } from '../types';
import { mockUser } from '../data/mockData';

interface UserContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  updateReadingProgress: (bookId: string, page: number, progress: number) => void;
  addToLibrary: (book: any) => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

export const UserProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(mockUser);

  const login = async (email: string, password: string) => {
    // Mock login - in real app, this would call an API
    setUser(mockUser);
  };

  const logout = () => {
    setUser(null);
  };

  const updateReadingProgress = (bookId: string, page: number, progress: number) => {
    if (!user) return;

    setUser(prev => {
      if (!prev) return prev;
      
      const updatedLibrary = prev.library.map(userBook => 
        userBook.bookId === bookId 
          ? { ...userBook, currentPage: page, progress, lastReadDate: new Date().toISOString() }
          : userBook
      );

      return { ...prev, library: updatedLibrary };
    });
  };

  const addToLibrary = (book: any) => {
    if (!user) return;

    const userBook: UserBook = {
      bookId: book.id,
      book,
      progress: 0,
      currentPage: 1,
      bookmarks: [],
      notes: [],
      purchaseDate: new Date().toISOString(),
      lastReadDate: new Date().toISOString()
    };

    setUser(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        library: [...prev.library, userBook]
      };
    });
  };

  return (
    <UserContext.Provider value={{ user, login, logout, updateReadingProgress, addToLibrary }}>
      {children}
    </UserContext.Provider>
  );
};