import React from 'react';
import { <PERSON>R<PERSON>, TrendingUp, Clock, Star, BookOpen } from 'lucide-react';
import { Link } from 'react-router-dom';
import BookCard from '../components/common/BookCard';
import { mockBooks, mockBundles } from '../data/mockData';
import { useUser } from '../contexts/UserContext';

const Home: React.FC = () => {
  const { user } = useUser();
  const featuredBooks = mockBooks.slice(0, 6);
  const trendingBooks = mockBooks.slice(2, 8);
  const currentlyReading = user?.library.filter(book => book.progress > 0 && book.progress < 100);

  const StatCard: React.FC<{
    icon: React.ElementType;
    title: string;
    value: string;
    description: string;
    color: string;
  }> = ({ icon: Icon, title, value, description, color }) => (
    <div className={`bg-gradient-to-br ${color} p-6 rounded-2xl text-white`}>
      <div className="flex items-center justify-between mb-4">
        <Icon size={24} />
        <span className="text-2xl font-bold">{value}</span>
      </div>
      <h3 className="font-semibold mb-1">{title}</h3>
      <p className="text-sm opacity-90">{description}</p>
    </div>
  );

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 rounded-3xl p-8 md:p-12 text-white overflow-hidden">
        <div className="relative z-10">
          <div className="max-w-2xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Keep the story going...
            </h1>
            <p className="text-xl mb-8 opacity-90">
              Don't let the story end just yet. Continue reading your last book and immerse yourself in the world of literature.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                to="/books"
                className="inline-flex items-center px-6 py-3 bg-white text-blue-600 font-semibold rounded-xl hover:bg-gray-100 transition-colors duration-200"
              >
                Start Reading
                <ArrowRight className="ml-2" size={20} />
              </Link>
              <Link
                to="/library"
                className="inline-flex items-center px-6 py-3 bg-white/20 backdrop-blur-sm text-white font-semibold rounded-xl hover:bg-white/30 transition-colors duration-200"
              >
                My Library
                <BookOpen className="ml-2" size={20} />
              </Link>
            </div>
          </div>
        </div>
        
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-10 w-32 h-32 border border-white rounded-full"></div>
          <div className="absolute bottom-10 right-32 w-20 h-20 border border-white rounded-full"></div>
          <div className="absolute top-32 right-64 w-16 h-16 border border-white rounded-full"></div>
        </div>
      </section>

      {/* Stats */}
      <section className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatCard
          icon={BookOpen}
          title="Books Read"
          value={user?.library.length.toString() || '0'}
          description="Your reading journey"
          color="from-green-500 to-green-600"
        />
        <StatCard
          icon={TrendingUp}
          title="Reading Streak"
          value="7"
          description="Days in a row"
          color="from-orange-500 to-orange-600"
        />
        <StatCard
          icon={Clock}
          title="Hours Read"
          value="24"
          description="This month"
          color="from-purple-500 to-purple-600"
        />
      </section>

      {/* Currently Reading */}
      {currentlyReading && currentlyReading.length > 0 && (
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Continue Reading
            </h2>
            <Link
              to="/library"
              className="text-blue-600 dark:text-blue-400 hover:underline font-medium"
            >
              View All
            </Link>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {currentlyReading.slice(0, 3).map((userBook) => (
              <div key={userBook.bookId} className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
                <div className="flex items-start space-x-4">
                  <img
                    src={userBook.book.cover}
                    alt={userBook.book.title}
                    className="w-16 h-20 object-cover rounded-lg"
                  />
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                      {userBook.book.title}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      by {userBook.book.author}
                    </p>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">Progress</span>
                        <span className="text-gray-900 dark:text-white font-medium">{userBook.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${userBook.progress}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Page {userBook.currentPage} of {userBook.book.pages}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Featured Books */}
      <section>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Star className="text-yellow-500" size={24} />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Featured Books
            </h2>
          </div>
          <Link
            to="/books"
            className="text-blue-600 dark:text-blue-400 hover:underline font-medium"
          >
            View All
          </Link>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {featuredBooks.map((book) => (
            <BookCard key={book.id} book={book} size="small" />
          ))}
        </div>
      </section>

      {/* Trending Now */}
      <section>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <TrendingUp className="text-green-500" size={24} />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Trending Now
            </h2>
          </div>
          <Link
            to="/trending"
            className="text-blue-600 dark:text-blue-400 hover:underline font-medium"
          >
            View All
          </Link>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {trendingBooks.slice(0, 4).map((book) => (
            <BookCard key={book.id} book={book} />
          ))}
        </div>
      </section>

      {/* Book Bundles */}
      <section>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Special Bundles
          </h2>
          <Link
            to="/bundles"
            className="text-blue-600 dark:text-blue-400 hover:underline font-medium"
          >
            View All
          </Link>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {mockBundles.map((bundle) => (
            <div key={bundle.id} className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-start space-x-6">
                <img
                  src={bundle.cover}
                  alt={bundle.title}
                  className="w-24 h-32 object-cover rounded-lg"
                />
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                    {bundle.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    {bundle.description}
                  </p>
                  <div className="flex items-center space-x-2 mb-4">
                    <span className="text-2xl font-bold text-green-600">
                      ${bundle.discountPrice}
                    </span>
                    <span className="text-lg text-gray-500 line-through">
                      ${bundle.originalPrice}
                    </span>
                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                      Save ${(bundle.originalPrice - bundle.discountPrice).toFixed(2)}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    Includes {bundle.books.length} books
                  </p>
                  <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-xl hover:bg-blue-700 transition-colors duration-200">
                    Get Bundle
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
};

export default Home;