export interface Book {
  id: string;
  title: string;
  author: string;
  cover: string;
  description: string;
  category: string;
  price: number;
  rating: number;
  reviews: number;
  pages: number;
  language: string;
  isbn: string;
  publishedDate: string;
  tags: string[];
  previewUrl?: string;
  chapters?: Chapter[];
}

export interface Chapter {
  id: string;
  title: string;
  content: string;
  pageStart: number;
  pageEnd: number;
}

export interface Bundle {
  id: string;
  title: string;
  description: string;
  books: Book[];
  originalPrice: number;
  discountPrice: number;
  cover: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  library: UserBook[];
  preferences: UserPreferences;
}

export interface UserBook {
  bookId: string;
  book: Book;
  progress: number;
  currentPage: number;
  bookmarks: Bookmark[];
  notes: Note[];
  purchaseDate: string;
  lastReadDate: string;
}

export interface Bookmark {
  id: string;
  page: number;
  note?: string;
  createdAt: string;
}

export interface Note {
  id: string;
  page: number;
  content: string;
  highlight: string;
  createdAt: string;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'sepia';
  fontSize: 'small' | 'medium' | 'large';
  fontFamily: 'serif' | 'sans-serif' | 'mono';
  lineHeight: 'tight' | 'normal' | 'relaxed';
  pageWidth: 'narrow' | 'medium' | 'wide';
}

export interface Review {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  bookId: string;
  rating: number;
  comment: string;
  createdAt: string;
  helpful: number;
}