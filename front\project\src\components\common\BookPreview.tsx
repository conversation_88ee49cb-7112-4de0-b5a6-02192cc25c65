import React, { useState } from 'react';
import { X, ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';

interface BookPreviewProps {
  book: any;
  isOpen: boolean;
  onClose: () => void;
}

const BookPreview: React.FC<BookPreviewProps> = ({ book, isOpen, onClose }) => {
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState(0);
  const [zoom, setZoom] = useState(1);
  
  // Mock preview pages
  const previewPages = [
    {
      type: 'cover',
      content: book?.cover,
      title: 'Cover'
    },
    {
      type: 'toc',
      content: 'Table of Contents',
      title: 'Table of Contents',
      items: [
        'Chapter 1: Introduction',
        'Chapter 2: Getting Started',
        'Chapter 3: Advanced Concepts',
        'Chapter 4: Best Practices',
        'Chapter 5: Conclusion'
      ]
    },
    {
      type: 'sample',
      content: `Chapter 1: Introduction

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.`,
      title: 'Sample Chapter'
    }
  ];

  const handlePrevPage = () => {
    setCurrentPage(prev => Math.max(0, prev - 1));
  };

  const handleNextPage = () => {
    setCurrentPage(prev => Math.min(previewPages.length - 1, prev + 1));
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(2, prev + 0.25));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(0.5, prev - 0.25));
  };

  const resetZoom = () => {
    setZoom(1);
  };

  if (!book) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white dark:bg-gray-800 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
            onClick={e => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                  {book.title}
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  by {book.author}
                </p>
              </div>
              
              <div className="flex items-center space-x-2">
                {/* Zoom Controls */}
                <button
                  onClick={handleZoomOut}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
                  title="Zoom Out"
                >
                  <ZoomOut size={20} />
                </button>
                <span className="text-sm text-gray-600 dark:text-gray-400 min-w-[60px] text-center">
                  {Math.round(zoom * 100)}%
                </span>
                <button
                  onClick={handleZoomIn}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
                  title="Zoom In"
                >
                  <ZoomIn size={20} />
                </button>
                <button
                  onClick={resetZoom}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
                  title="Reset Zoom"
                >
                  <RotateCcw size={20} />
                </button>
                
                <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2" />
                
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
                >
                  <X size={20} />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-hidden">
              <div className="h-[60vh] overflow-auto p-6">
                <div 
                  className="transition-transform duration-200 origin-top-left"
                  style={{ transform: `scale(${zoom})` }}
                >
                  {previewPages[currentPage]?.type === 'cover' && (
                    <div className="flex justify-center">
                      <img
                        src={previewPages[currentPage].content}
                        alt={book.title}
                        className="max-w-md rounded-lg shadow-lg"
                      />
                    </div>
                  )}
                  
                  {previewPages[currentPage]?.type === 'toc' && (
                    <div className="max-w-2xl mx-auto">
                      <h3 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
                        Table of Contents
                      </h3>
                      <div className="space-y-3">
                        {previewPages[currentPage].items?.map((item, index) => (
                          <div key={index} className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                            <span className="text-gray-900 dark:text-white">{item}</span>
                            <span className="text-gray-500 dark:text-gray-400">{index + 1}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {previewPages[currentPage]?.type === 'sample' && (
                    <div className="max-w-2xl mx-auto prose dark:prose-invert">
                      <div className="whitespace-pre-line text-gray-900 dark:text-white leading-relaxed">
                        {previewPages[currentPage].content}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Footer Navigation */}
            <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={handlePrevPage}
                disabled={currentPage === 0}
                className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeft size={20} />
                <span>Previous</span>
              </button>
              
              <div className="flex items-center space-x-2">
                {previewPages.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentPage(index)}
                    className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                      index === currentPage
                        ? 'bg-blue-600'
                        : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
                    }`}
                  />
                ))}
              </div>
              
              <button
                onClick={handleNextPage}
                disabled={currentPage === previewPages.length - 1}
                className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span>Next</span>
                <ChevronRight size={20} />
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default BookPreview;