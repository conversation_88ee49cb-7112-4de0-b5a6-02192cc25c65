import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import localforage from 'localforage';

interface OfflineContextType {
  isOnline: boolean;
  downloadedBooks: string[];
  downloadBook: (bookId: string, bookData: any) => Promise<void>;
  removeDownload: (bookId: string) => Promise<void>;
  isBookDownloaded: (bookId: string) => boolean;
  syncData: () => Promise<void>;
  isSyncing: boolean;
}

const OfflineContext = createContext<OfflineContextType | undefined>(undefined);

export const useOffline = () => {
  const context = useContext(OfflineContext);
  if (!context) {
    throw new Error('useOffline must be used within an OfflineProvider');
  }
  return context;
};

export const OfflineProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [downloadedBooks, setDownloadedBooks] = useState<string[]>([]);
  const [isSyncing, setIsSyncing] = useState(false);

  // Configure localforage
  useEffect(() => {
    localforage.config({
      name: 'AstewaiLibrary',
      storeName: 'books'
    });
    
    // Load downloaded books list
    loadDownloadedBooks();
  }, []);

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      syncData();
    };
    
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const loadDownloadedBooks = async () => {
    try {
      const books = await localforage.getItem<string[]>('downloadedBooks') || [];
      setDownloadedBooks(books);
    } catch (error) {
      console.error('Failed to load downloaded books:', error);
    }
  };

  const downloadBook = useCallback(async (bookId: string, bookData: any) => {
    try {
      // Store book data
      await localforage.setItem(`book_${bookId}`, bookData);
      
      // Update downloaded books list
      const updatedBooks = [...downloadedBooks, bookId];
      await localforage.setItem('downloadedBooks', updatedBooks);
      setDownloadedBooks(updatedBooks);
      
      console.log(`Book ${bookId} downloaded successfully`);
    } catch (error) {
      console.error('Failed to download book:', error);
      throw error;
    }
  }, [downloadedBooks]);

  const removeDownload = useCallback(async (bookId: string) => {
    try {
      // Remove book data
      await localforage.removeItem(`book_${bookId}`);
      
      // Update downloaded books list
      const updatedBooks = downloadedBooks.filter(id => id !== bookId);
      await localforage.setItem('downloadedBooks', updatedBooks);
      setDownloadedBooks(updatedBooks);
      
      console.log(`Book ${bookId} removed from downloads`);
    } catch (error) {
      console.error('Failed to remove download:', error);
      throw error;
    }
  }, [downloadedBooks]);

  const isBookDownloaded = useCallback((bookId: string) => {
    return downloadedBooks.includes(bookId);
  }, [downloadedBooks]);

  const syncData = useCallback(async () => {
    if (!isOnline) return;
    
    setIsSyncing(true);
    try {
      // Sync reading progress, bookmarks, notes, etc.
      const readingProgress = await localforage.getItem('readingProgress');
      const bookmarks = await localforage.getItem('bookmarks');
      const notes = await localforage.getItem('notes');
      
      // In a real app, you would send this data to your backend
      console.log('Syncing data:', { readingProgress, bookmarks, notes });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('Data synced successfully');
    } catch (error) {
      console.error('Failed to sync data:', error);
    } finally {
      setIsSyncing(false);
    }
  }, [isOnline]);

  return (
    <OfflineContext.Provider value={{
      isOnline,
      downloadedBooks,
      downloadBook,
      removeDownload,
      isBookDownloaded,
      syncData,
      isSyncing
    }}>
      {children}
    </OfflineContext.Provider>
  );
};