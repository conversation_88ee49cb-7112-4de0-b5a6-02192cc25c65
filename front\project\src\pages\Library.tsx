import React, { useState } from 'react';
import { Book<PERSON><PERSON>, Clock, Star, MoreVertical, Play, Bookmark } from 'lucide-react';
import { useUser } from '../contexts/UserContext';
import { Link } from 'react-router-dom';

const Library: React.FC = () => {
  const { user } = useUser();
  const [filter, setFilter] = useState<'all' | 'reading' | 'completed' | 'wishlist'>('all');

  if (!user) {
    return (
      <div className="text-center py-12">
        <BookOpen size={64} className="mx-auto text-gray-400 mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Sign in to view your library
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Access your purchased books, reading progress, and bookmarks.
        </p>
        <button className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200">
          Sign In
        </button>
      </div>
    );
  }

  const filteredBooks = user.library.filter(userBook => {
    switch (filter) {
      case 'reading':
        return userBook.progress > 0 && userBook.progress < 100;
      case 'completed':
        return userBook.progress === 100;
      case 'wishlist':
        return false; // Implement wishlist later
      default:
        return true;
    }
  });

  const stats = {
    total: user.library.length,
    reading: user.library.filter(book => book.progress > 0 && book.progress < 100).length,
    completed: user.library.filter(book => book.progress === 100).length,
    totalPages: user.library.reduce((sum, book) => sum + book.book.pages, 0)
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">My Library</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Your personal collection of books and reading progress
          </p>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <BookOpen className="text-blue-600" size={24} />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">
              {stats.total}
            </span>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Total Books</p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <Clock className="text-orange-600" size={24} />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">
              {stats.reading}
            </span>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Currently Reading</p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <Star className="text-green-600" size={24} />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">
              {stats.completed}
            </span>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Completed</p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <BookOpen className="text-purple-600" size={24} />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">
              {stats.totalPages.toLocaleString()}
            </span>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Total Pages</p>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
        {[
          { key: 'all', label: 'All Books' },
          { key: 'reading', label: 'Currently Reading' },
          { key: 'completed', label: 'Completed' },
          { key: 'wishlist', label: 'Wishlist' }
        ].map(({ key, label }) => (
          <button
            key={key}
            onClick={() => setFilter(key as any)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
              filter === key
                ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            {label}
          </button>
        ))}
      </div>

      {/* Books List */}
      {filteredBooks.length > 0 ? (
        <div className="grid grid-cols-1 gap-4">
          {filteredBooks.map((userBook) => (
            <div key={userBook.bookId} className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-start space-x-6">
                <img
                  src={userBook.book.cover}
                  alt={userBook.book.title}
                  className="w-20 h-28 object-cover rounded-lg"
                />
                
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-1">
                        {userBook.book.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        by {userBook.book.author}
                      </p>
                    </div>
                    <button className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                      <MoreVertical size={20} />
                    </button>
                  </div>

                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
                    {userBook.book.description}
                  </p>

                  {/* Progress */}
                  <div className="mb-4">
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-gray-600 dark:text-gray-400">Reading Progress</span>
                      <span className="text-gray-900 dark:text-white font-medium">
                        {userBook.progress}% • Page {userBook.currentPage} of {userBook.book.pages}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${userBook.progress}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-3">
                    <Link
                      to={`/read/${userBook.bookId}`}
                      className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200"
                    >
                      <Play size={16} />
                      <span>{userBook.progress > 0 ? 'Continue Reading' : 'Start Reading'}</span>
                    </Link>
                    
                    <button className="flex items-center space-x-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                      <Bookmark size={16} />
                      <span>Bookmarks ({userBook.bookmarks.length})</span>
                    </button>
                    
                    <div className="flex items-center space-x-1 text-sm text-gray-500 dark:text-gray-400">
                      <Clock size={16} />
                      <span>Last read {new Date(userBook.lastReadDate).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <BookOpen size={64} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            {filter === 'all' ? 'Your library is empty' : `No ${filter} books`}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {filter === 'all' 
              ? 'Start building your library by exploring our collection.'
              : `You don't have any ${filter} books yet.`
            }
          </p>
          <Link
            to="/books"
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200"
          >
            Browse Books
          </Link>
        </div>
      )}
    </div>
  );
};

export default Library;